<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VitalFlow - 健康管理App UI设计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#06b6d4',
                            600: '#0891b2',
                            700: '#0e7490'
                        },
                        health: {
                            green: '#10b981',
                            orange: '#f59e0b',
                            purple: '#8b5cf6',
                            pink: '#ec4899'
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 font-sans">
    <div class="container mx-auto p-6">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">VitalFlow - 健康管理App UI设计</h1>
        
        <!-- 页面预览容器 -->
        <div class="flex flex-wrap gap-6 justify-center">
            
            <!-- 启动页 -->
            <div class="mockup-container">
                <h3 class="mockup-title">启动页 (Splash Screen)</h3>
                <div class="mockup-phone">
                    <div class="bg-gradient-to-br from-primary-500 to-primary-700 h-full flex flex-col items-center justify-center text-white relative overflow-hidden">
                        <!-- 背景装饰 -->
                        <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                        <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
                        
                        <!-- Logo区域 -->
                        <div class="text-center z-10">
                            <div class="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mb-6 mx-auto backdrop-blur-sm">
                                <i class="fas fa-heartbeat text-3xl text-white"></i>
                            </div>
                            <h1 class="text-3xl font-bold mb-2">VitalFlow</h1>
                            <p class="text-primary-100 text-sm font-medium">智能健康管理</p>
                        </div>
                        
                        <!-- 加载指示器 -->
                        <div class="absolute bottom-20 left-1/2 transform -translate-x-1/2">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-white/60 rounded-full animate-pulse"></div>
                                <div class="w-2 h-2 bg-white/60 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                                <div class="w-2 h-2 bg-white/60 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 登录页 -->
            <div class="mockup-container">
                <h3 class="mockup-title">登录页</h3>
                <div class="mockup-phone">
                    <div class="bg-white h-full flex flex-col">
                        <!-- 顶部装饰 -->
                        <div class="bg-gradient-to-br from-primary-500 to-primary-600 h-48 relative overflow-hidden">
                            <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                            <div class="flex items-center justify-center h-full">
                                <div class="text-center text-white">
                                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mb-4 mx-auto">
                                        <i class="fas fa-heartbeat text-2xl"></i>
                                    </div>
                                    <h2 class="text-2xl font-bold">欢迎回来</h2>
                                    <p class="text-primary-100 text-sm">开始您的健康之旅</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 登录表单 -->
                        <div class="flex-1 px-6 py-8">
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                                    <div class="relative">
                                        <input type="tel" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="请输入手机号">
                                        <i class="fas fa-phone absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">验证码</label>
                                    <div class="flex space-x-3">
                                        <input type="text" class="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="验证码">
                                        <button class="px-4 py-3 bg-primary-500 text-white rounded-xl text-sm font-medium">获取验证码</button>
                                    </div>
                                </div>
                                
                                <button class="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 rounded-xl font-medium text-lg shadow-lg shadow-primary-500/25">
                                    登录
                                </button>
                                
                                <div class="text-center">
                                    <p class="text-gray-500 text-sm">
                                        没有账号？ 
                                        <a href="#" class="text-primary-500 font-medium">立即注册</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 健康仪表板 -->
            <div class="mockup-container">
                <h3 class="mockup-title">健康仪表板</h3>
                <div class="mockup-phone">
                    <div class="bg-gray-50 h-full flex flex-col">
                        <!-- 顶部状态栏 -->
                        <div class="bg-white px-6 py-4 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h1 class="text-xl font-bold text-gray-800">早上好，张三</h1>
                                    <p class="text-sm text-gray-500">今天是美好的一天</p>
                                </div>
                                <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 健康评分卡片 -->
                        <div class="px-6 py-4">
                            <div class="bg-gradient-to-br from-health-green to-emerald-600 rounded-2xl p-6 text-white relative overflow-hidden">
                                <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
                                <div class="relative z-10">
                                    <div class="flex items-center justify-between mb-4">
                                        <div>
                                            <p class="text-green-100 text-sm">今日健康评分</p>
                                            <p class="text-3xl font-bold">85</p>
                                        </div>
                                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                                            <i class="fas fa-chart-line text-2xl"></i>
                                        </div>
                                    </div>
                                    <p class="text-green-100 text-sm">比昨天提升了 5 分 ↗️</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 快捷功能 -->
                        <div class="px-6">
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-white rounded-xl p-4 shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-health-orange/10 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-running text-health-orange"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">今日步数</p>
                                            <p class="text-lg font-bold text-gray-800">8,432</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-white rounded-xl p-4 shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-health-purple/10 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-moon text-health-purple"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">睡眠时长</p>
                                            <p class="text-lg font-bold text-gray-800">7.5h</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 底部导航 -->
                        <div class="mt-auto bg-white border-t border-gray-100 px-6 py-3">
                            <div class="flex justify-around">
                                <div class="flex flex-col items-center space-y-1">
                                    <i class="fas fa-home text-primary-500"></i>
                                    <span class="text-xs text-primary-500 font-medium">首页</span>
                                </div>
                                <div class="flex flex-col items-center space-y-1">
                                    <i class="fas fa-chart-bar text-gray-400"></i>
                                    <span class="text-xs text-gray-400">统计</span>
                                </div>
                                <div class="flex flex-col items-center space-y-1">
                                    <i class="fas fa-plus-circle text-gray-400"></i>
                                    <span class="text-xs text-gray-400">记录</span>
                                </div>
                                <div class="flex flex-col items-center space-y-1">
                                    <i class="fas fa-user text-gray-400"></i>
                                    <span class="text-xs text-gray-400">我的</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </div>

    <style>
        .mockup-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .mockup-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .mockup-phone {
            width: 280px;
            height: 580px;
            background: #000;
            border-radius: 2rem;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            position: relative;
        }
        
        .mockup-phone::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: #333;
            border-radius: 2px;
            z-index: 10;
        }
        
        .mockup-phone > div {
            border-radius: 1.5rem;
            overflow: hidden;
        }
    </style>
</body>
</html>
